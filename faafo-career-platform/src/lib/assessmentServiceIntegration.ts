/**
 * Assessment Service Integration Layer
 * Provides a clean interface for integrating algorithmic assessment with existing services
 */

import { AssessmentResponse, AssessmentInsights } from './assessmentScoring';
import { AlgorithmicAssessmentService, AlgorithmicMatchResult } from './algorithmicAssessmentService';
import { EnhancedFallbackService, FallbackInsights } from './enhancedFallbackService';

export interface IntegratedAssessmentResult {
  careerRecommendations: string[];
  detailedMatches?: AlgorithmicMatchResult[];
  fallbackInsights?: FallbackInsights;
  serviceUsed: 'algorithmic' | 'fallback' | 'basic';
  confidence: number;
  processingTime: number;
}

export class AssessmentServiceIntegration {
  private static initialized = false;

  /**
   * Initialize the assessment services
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await AlgorithmicAssessmentService.initialize();
      this.initialized = true;
      console.log('Assessment services initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize assessment services:', error);
      // Continue without algorithmic service
    }
  }

  /**
   * Generate career recommendations using the best available service
   */
  static async generateCareerRecommendations(
    responses: AssessmentResponse,
    insights: AssessmentInsights
  ): Promise<IntegratedAssessmentResult> {
    const startTime = Date.now();
    
    // Ensure services are initialized
    await this.initialize();

    try {
      // Try algorithmic assessment first
      const algorithmicMatches = await AlgorithmicAssessmentService.generateCareerRecommendations(
        responses,
        insights
      );

      const careerRecommendations = algorithmicMatches
        .slice(0, 5)
        .map(match => match.careerPath.name);

      const processingTime = Date.now() - startTime;

      return {
        careerRecommendations,
        detailedMatches: algorithmicMatches,
        serviceUsed: 'algorithmic',
        confidence: this.calculateAverageConfidence(algorithmicMatches),
        processingTime
      };

    } catch (algorithmicError) {
      console.warn('Algorithmic service failed, trying enhanced fallback:', algorithmicError);

      try {
        // Try enhanced fallback service
        const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
          responses,
          insights,
          'Algorithmic service unavailable'
        );

        const careerRecommendations = fallbackInsights.careerRecommendations
          .slice(0, 5)
          .map(rec => rec.careerPath);

        const processingTime = Date.now() - startTime;

        return {
          careerRecommendations,
          fallbackInsights,
          serviceUsed: 'fallback',
          confidence: fallbackInsights.confidenceScore,
          processingTime
        };

      } catch (fallbackError) {
        console.warn('Enhanced fallback failed, using basic fallback:', fallbackError);

        // Use basic rule-based fallback
        const careerRecommendations = this.generateBasicCareerSuggestions(
          insights.topSkills,
          responses
        );

        const processingTime = Date.now() - startTime;

        return {
          careerRecommendations,
          serviceUsed: 'basic',
          confidence: 60, // Basic confidence for rule-based approach
          processingTime
        };
      }
    }
  }

  /**
   * Get enhanced assessment insights with fallback support
   */
  static async getEnhancedInsights(
    responses: AssessmentResponse,
    insights: AssessmentInsights
  ): Promise<FallbackInsights | null> {
    try {
      return await EnhancedFallbackService.generateFallbackInsights(
        responses,
        insights,
        'Enhanced insights request'
      );
    } catch (error) {
      console.warn('Failed to generate enhanced insights:', error);
      return null;
    }
  }

  /**
   * Calculate average confidence from algorithmic matches
   */
  private static calculateAverageConfidence(matches: AlgorithmicMatchResult[]): number {
    if (matches.length === 0) return 50;

    const totalConfidence = matches.reduce((sum, match) => sum + match.confidenceLevel, 0);
    return Math.round(totalConfidence / matches.length);
  }

  /**
   * Basic career suggestions as final fallback
   */
  private static generateBasicCareerSuggestions(
    topSkills: string[],
    responses: AssessmentResponse
  ): string[] {
    const suggestions: string[] = [];
    const skillDevelopmentInterests = this.getArrayValue(responses.skill_development_interest);
    const careerValues = this.getArrayValue(responses.career_values);
    
    // Technical programming paths
    if (topSkills.includes('technical_programming')) {
      suggestions.push('Full-Stack Web Developer', 'Mobile App Developer');
      
      if (skillDevelopmentInterests.includes('ai_ml') || topSkills.includes('data_analysis')) {
        suggestions.push('AI/ML Engineer', 'Data Scientist');
      }
      
      if (skillDevelopmentInterests.includes('cybersecurity')) {
        suggestions.push('Cybersecurity Specialist');
      }
    }

    // Creative and content paths
    if (topSkills.includes('writing_content') || topSkills.includes('design_creative')) {
      suggestions.push('UX/UI Designer', 'Content Creator');
      
      if (skillDevelopmentInterests.includes('digital_marketing')) {
        suggestions.push('Digital Marketing Specialist');
      }
    }

    // Business and leadership paths
    if (topSkills.includes('sales_marketing') || topSkills.includes('leadership')) {
      suggestions.push('Digital Marketing Specialist', 'Product Manager');
      
      if (skillDevelopmentInterests.includes('entrepreneurship') || careerValues.includes('autonomy')) {
        suggestions.push('Entrepreneur / Startup Founder');
      }
    }

    // Education and coaching paths
    if (topSkills.includes('teaching_training') || topSkills.includes('coaching_mentoring')) {
      suggestions.push('Online Coaching', 'Course Creation', 'Training Specialist');
    }

    // Data and analysis paths
    if (topSkills.includes('data_analysis')) {
      suggestions.push('Data Scientist', 'Business Analyst');
      
      if (skillDevelopmentInterests.includes('financial_planning')) {
        suggestions.push('Financial Advisor / Planner');
      }
    }

    // Project management paths
    if (topSkills.includes('project_management')) {
      suggestions.push('Product Manager', 'Project Manager', 'Scrum Master');
    }

    // Remove duplicates and return top suggestions
    const uniqueSuggestions = Array.from(new Set(suggestions));
    
    // If still no matches, provide diverse default options
    if (uniqueSuggestions.length === 0) {
      return [
        'Digital Marketing Specialist',
        'Full-Stack Web Developer', 
        'Product Manager',
        'Data Analyst',
        'UX/UI Designer'
      ];
    }
    
    return uniqueSuggestions.slice(0, 5);
  }

  /**
   * Utility method for extracting array values
   */
  private static getArrayValue(value: string | string[] | number | null): string[] {
    if (Array.isArray(value)) return value.filter(v => typeof v === 'string') as string[];
    if (typeof value === 'string') return [value];
    return [];
  }

  /**
   * Health check for assessment services
   */
  static async healthCheck(): Promise<{
    algorithmic: boolean;
    fallback: boolean;
    basic: boolean;
    overall: 'healthy' | 'degraded' | 'critical';
  }> {
    let algorithmicHealthy = false;
    let fallbackHealthy = false;
    const basicHealthy = true; // Basic service is always available

    try {
      await this.initialize();
      // Test algorithmic service with minimal data
      const testResponse: AssessmentResponse = {
        current_role: 'Test',
        years_experience: '1-2',
        skill_development_interest: ['coding_tech'],
        career_values: ['growth'],
        work_style_preferences: ['remote'],
        biggest_obstacles: ['skill_gaps'],
        financial_readiness: 3,
        support_level: 3,
        risk_tolerance: 3,
        urgency_level: 3,
        skills_confidence: 50,
        desired_outcomes_work_life: 'Test',
        desired_outcomes_financial: 'Test',
        desired_outcomes_personal: 'Test',
        location: 'Test'
      };

      const testInsights: AssessmentInsights = {
        scores: {
          financialReadiness: 3,
          supportLevel: 3,
          riskTolerance: 3,
          urgencyLevel: 3,
          skillsConfidence: 50,
          readinessScore: 60
        },
        primaryMotivation: 'Test',
        topSkills: ['technical_programming'],
        biggestObstacles: ['skill_gaps'],
        recommendedTimeline: '6-12 months',
        keyRecommendations: ['Test'],
        careerPathSuggestions: [],
        careerPathAnalysis: [],
        overallSkillGaps: [],
        learningPriorities: ['technical_programming'],
        estimatedTransitionTime: '6-12 months'
      };

      const algorithmicResult = await AlgorithmicAssessmentService.generateCareerRecommendations(
        testResponse,
        testInsights
      );
      algorithmicHealthy = algorithmicResult.length > 0;
    } catch (error) {
      console.warn('Algorithmic service health check failed:', error);
    }

    try {
      const fallbackResult = await EnhancedFallbackService.generateFallbackInsights(
        {
          current_role: 'Test',
          years_experience: '1-2',
          skill_development_interest: ['coding_tech'],
          career_values: ['growth'],
          work_style_preferences: ['remote'],
          biggest_obstacles: ['skill_gaps'],
          financial_readiness: 3,
          support_level: 3,
          risk_tolerance: 3,
          urgency_level: 3,
          skills_confidence: 50,
          desired_outcomes_work_life: 'Test',
          desired_outcomes_financial: 'Test',
          desired_outcomes_personal: 'Test',
          location: 'Test'
        },
        {
          scores: {
            financialReadiness: 3,
            supportLevel: 3,
            riskTolerance: 3,
            urgencyLevel: 3,
            skillsConfidence: 50,
            readinessScore: 60
          },
          primaryMotivation: 'Test',
          topSkills: ['technical_programming'],
          biggestObstacles: ['skill_gaps'],
          recommendedTimeline: '6-12 months',
          keyRecommendations: ['Test'],
          careerPathSuggestions: [],
          careerPathAnalysis: [],
          overallSkillGaps: [],
          learningPriorities: ['technical_programming'],
          estimatedTransitionTime: '6-12 months'
        },
        'Health check'
      );
      fallbackHealthy = fallbackResult.careerRecommendations.length > 0;
    } catch (error) {
      console.warn('Fallback service health check failed:', error);
    }

    let overall: 'healthy' | 'degraded' | 'critical';
    if (algorithmicHealthy && fallbackHealthy) {
      overall = 'healthy';
    } else if (fallbackHealthy || basicHealthy) {
      overall = 'degraded';
    } else {
      overall = 'critical';
    }

    return {
      algorithmic: algorithmicHealthy,
      fallback: fallbackHealthy,
      basic: basicHealthy,
      overall
    };
  }
}
